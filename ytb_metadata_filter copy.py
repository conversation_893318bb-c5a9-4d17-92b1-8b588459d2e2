import re
import json
import requests
from datetime import datetime
import random
import string
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger
import signal
import sys

PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"

class ProxyManager:
    """代理管理器，支持随机sessionid生成"""

    def __init__(self):
        self.test123 = True

    def _generate_session_id(self):
        """生成16位随机sessionid"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=16))

    def _generate_proxy_config(self):
        """生成带有随机sessionid的代理配置"""
        session_id = self._generate_session_id()
        # 只配置http代理，参考工作的脚本格式
        proxy_url = f"http://{PROXY_USER}-session-{session_id}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"

        return {
            'http': proxy_url,
        }

    def get_proxy_config(self):
        """获取新的代理配置"""
        return self._generate_proxy_config()


class ConcurrentVideoProcessor:
    """并发视频处理器"""

    def __init__(self, max_workers=20, use_proxy=True, timeout=30):
        self.max_workers = max_workers
        self.use_proxy = use_proxy
        self.timeout = timeout
        self.proxy_manager = ProxyManager() if use_proxy else None
        self.shutdown_event = threading.Event()
        self.write_lock = threading.Lock()

        # 统计信息
        self.total_processed = 0
        self.total_success = 0
        self.total_failed = 0
        self.start_time = None

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info(f"初始化并发处理器: max_workers={max_workers}, use_proxy={use_proxy}, timeout={timeout}")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.warning(f"收到信号 {signum}，正在优雅关闭...")
        self.shutdown_event.set()

    def process_single_video(self, video_id):
        """处理单个视频"""
        try:
            # 获取代理配置 - 每次请求都生成新的代理配置
            proxy_config = self.proxy_manager.get_proxy_config() if self.use_proxy else None

            if proxy_config and self.use_proxy:
                logger.debug(f"使用代理处理 {video_id}: {proxy_config['http'][:50]}...")

            # 获取视频信息
            results = get_video_info(video_id, proxy_config, self.timeout)

            if isinstance(results, list) and results:
                # 成功获取到视频信息
                self.total_success += 1
                logger.success(f"成功处理 {video_id}: 找到 {len(results)} 个符合条件的流")
                return results
            elif isinstance(results, dict) and "error" in results:
                # 处理失败
                self.total_failed += 1
                logger.error(f"处理失败 {video_id}: {results['error']}")
                return None
            else:
                # 没有符合条件的流
                self.total_failed += 1
                logger.warning(f"没有符合条件的流 {video_id}")
                return None

        except Exception as e:
            self.total_failed += 1
            logger.error(f"处理 {video_id} 时发生异常: {e}")
            return None
        finally:
            self.total_processed += 1

    def print_progress(self):
        """打印进度信息"""
        while not self.shutdown_event.is_set():
            time.sleep(10)  # 每10秒打印一次进度

            if self.start_time:
                elapsed = time.time() - self.start_time
                rate = self.total_processed / elapsed if elapsed > 0 else 0

                logger.info(f"进度: 已处理 {self.total_processed}, 成功 {self.total_success}, "
                          f"失败 {self.total_failed}, 速率 {rate:.2f}/秒")

    def process_videos_concurrent(self, video_ids, output_file="filtered_videos.jsonl"):
        """并发处理视频列表"""
        if not video_ids:
            logger.warning("没有视频需要处理")
            return

        self.start_time = time.time()
        logger.info(f"开始并发处理 {len(video_ids)} 个视频，使用 {self.max_workers} 个线程")

        # 启动进度监控线程
        progress_thread = threading.Thread(target=self.print_progress, daemon=True)
        progress_thread.start()

        # 使用线程池处理视频
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_video = {
                executor.submit(self.process_single_video, video_id): video_id
                for video_id in video_ids
            }

            # 处理完成的任务并写入文件 - 使用追加模式
            with open(output_file, "a", encoding="utf-8") as f:
                try:
                    for future in as_completed(future_to_video):
                        # 检查是否收到关闭信号
                        if self.shutdown_event.is_set():
                            logger.warning("收到关闭信号，正在取消剩余任务...")
                            # 取消所有未完成的任务
                            for remaining_future in future_to_video:
                                if not remaining_future.done():
                                    remaining_future.cancel()
                            break

                        video_id = future_to_video[future]
                        try:
                            results = future.result(timeout=1)
                            if results:
                                with self.write_lock:
                                    for item in results:
                                        f.write(json.dumps(item, ensure_ascii=False) + "\n")
                                        f.flush()  # 确保数据及时写入
                        except Exception as e:
                            logger.error(f"获取 {video_id} 结果时发生异常: {e}")

                except KeyboardInterrupt:
                    logger.warning("收到键盘中断信号，正在停止处理...")
                    self.shutdown_event.set()
                    # 取消所有未完成的任务
                    for remaining_future in future_to_video:
                        if not remaining_future.done():
                            remaining_future.cancel()

        # 停止进度监控
        self.shutdown_event.set()

        # 打印最终统计
        elapsed = time.time() - self.start_time
        rate = self.total_processed / elapsed if elapsed > 0 else 0
        logger.info(f"处理完成! 总计: {self.total_processed}, 成功: {self.total_success}, "
                   f"失败: {self.total_failed}, 平均速率: {rate:.2f}/秒, 耗时: {elapsed:.2f}秒")


def parse_year(date_str):
    """通用年份解析，支持 ISO8601、YYYY-MM-DD、中文年月日"""
    if not date_str:
        return None
    try:
        # ISO8601 格式 (2009-10-24T23:57:33-07:00)
        if "T" in date_str:
            return datetime.fromisoformat(date_str.replace("Z", "+00:00")).year
        # YYYY-MM-DD
        if re.match(r"^\d{4}-\d{2}-\d{2}$", date_str):
            return int(date_str.split("-")[0])
        # 中文格式 2015年2月18日
        match = re.search(r"(\d{4})年", date_str)
        if match:
            return int(match.group(1))
    except Exception:
        return None
    return None


def get_video_info(video_id, proxy_config=None, timeout=30):
    """获取单个视频信息，支持代理配置"""
    url = f"https://www.youtube.com/watch?v={video_id}"
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "en-US,en;q=0.9",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    }

    try:
        # 添加session以保持连接
        session = requests.Session()
        session.headers.update(headers)

        resp = session.get(
            url,
            proxies=proxy_config,
            timeout=timeout,
            allow_redirects=True
        )
        resp.raise_for_status()

        # 检查响应内容
        if len(resp.text) < 1000:
            logger.warning(f"响应内容过短 {video_id}: {len(resp.text)} 字符")
            return {"video_id": video_id, "error": "Response too short"}

    except requests.exceptions.ProxyError as e:
        logger.error(f"代理错误 {video_id}: {str(e)}")
        return {"video_id": video_id, "error": f"Proxy error: {str(e)}"}
    except requests.exceptions.Timeout as e:
        logger.error(f"请求超时 {video_id}: {str(e)}")
        return {"video_id": video_id, "error": f"Timeout: {str(e)}"}
    except requests.exceptions.ConnectionError as e:
        logger.error(f"连接错误 {video_id}: {str(e)}")
        return {"video_id": video_id, "error": f"Connection error: {str(e)}"}
    except Exception as e:
        logger.error(f"请求失败 {video_id}: {str(e)}")
        return {"video_id": video_id, "error": str(e)}
    
    match = re.search(r'ytInitialPlayerResponse\s*=\s*(\{.*?\});', resp.text)
    if not match:
        return {"video_id": video_id, "error": "No playerResponse"}
    
    data = json.loads(match.group(1))
    
    video_details = data.get("videoDetails", {})
    streaming_data = data.get("streamingData", {})
    formats = streaming_data.get("adaptiveFormats", [])
    
    # 标题 & 作者
    title = video_details.get("title")
    author = video_details.get("author")
    
    # 发布时间兼容
    microformat = data.get("microformat", {}).get("playerMicroformatRenderer", {})
    publish_date = (
        microformat.get("publishDate") or 
        microformat.get("uploadDate") or
        video_details.get("publishDate", {}).get("simpleText")
    )
    
    year = parse_year(publish_date)
    
    # 筛选满足条件的流并获取最大码率
    valid_streams = []
    for fmt in formats:
        height = fmt.get("height", 0)
        bitrate = fmt.get("bitrate", 0)
        if height >= 1080 and bitrate >= 1500 and (year is None or year >= 2020):
            valid_streams.append({
                "video_id": video_id,
                "quality": f"{height}p",
                "year": year,
                "bitrate": bitrate,
                "height": height
            })

    # 如果没有符合条件的流，返回空列表
    if not valid_streams:
        return []

    # 获取最大码率的流
    max_bitrate_stream = max(valid_streams, key=lambda x: x['bitrate'])

    # 移除临时的height字段
    max_bitrate_stream.pop('height', None)

    return [max_bitrate_stream]


def filter_videos(video_ids, output_file="filtered_videos.jsonl"):
    """原始的串行处理方法（保留兼容性）"""
    with open(output_file, "a", encoding="utf-8") as f:
        for vid in video_ids:
            results = get_video_info(vid)
            if results and isinstance(results, list):
                for item in results:
                    f.write(json.dumps(item, ensure_ascii=False) + "\n")


def filter_videos_concurrent(video_ids, output_file="filtered_videos.jsonl",
                           max_workers=20, use_proxy=False, timeout=30):
    """并发处理方法"""
    processor = ConcurrentVideoProcessor(
        max_workers=max_workers,
        use_proxy=use_proxy,
        timeout=timeout
    )
    processor.process_videos_concurrent(video_ids, output_file)


def read_video_ids_from_json(file_path="test_videos.json"):
    """从test_videos.json文件中读取video_id和short_id"""
    video_ids = []
    short_ids = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)

                    # 获取video_id列表
                    if 'video_id' in data and isinstance(data['video_id'], list):
                        video_ids.extend(data['video_id'])

                    # 获取short_id列表
                    if 'short_id' in data and isinstance(data['short_id'], list):
                        short_ids.extend(data['short_id'])

                except json.JSONDecodeError as e:
                    logger.error(f"第{line_num}行JSON解析失败: {e}")
                    continue

    except FileNotFoundError:
        logger.error(f"文件 {file_path} 不存在")
        return [], []
    except Exception as e:
        logger.error(f"读取文件时发生错误: {e}")
        return [], []

    # 去重并过滤有效的ID
    unique_video_ids = []
    unique_short_ids = []

    seen_video = set()
    for vid in video_ids:
        vid = str(vid).strip()
        if vid and len(vid) == 11 and vid not in seen_video:
            unique_video_ids.append(vid)
            seen_video.add(vid)

    seen_short = set()
    for sid in short_ids:
        sid = str(sid).strip()
        if sid and len(sid) == 11 and sid not in seen_short:
            unique_short_ids.append(sid)
            seen_short.add(sid)

    logger.info(f"从 {file_path} 读取到:")
    logger.info(f"  - video_id: {len(video_ids)} 个，去重后 {len(unique_video_ids)} 个")
    logger.info(f"  - short_id: {len(short_ids)} 个，去重后 {len(unique_short_ids)} 个")

    return unique_video_ids, unique_short_ids


if __name__ == "__main__":
    # 配置日志
    logger.remove()  # 移除默认处理器
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )

    # 从test_videos.json读取视频ID
    video_ids, short_ids = read_video_ids_from_json("/root/novel/ytb_bozhu/all_result.jsonl")

    if not video_ids and not short_ids:
        logger.error("没有找到有效的视频ID")
        # 使用默认测试视频ID列表作为备选
        video_ids = [
            "zTiyRbrrLzc",  # 例子
            "dQw4w9WgXcQ",  # rickroll
        ]
        logger.info("使用默认测试视频ID列表")

    # 合并video_id和short_id
    all_video_ids = video_ids + short_ids
    logger.info(f"总共需要处理 {len(all_video_ids)} 个视频ID")

    # choice = input("请输入选择 (1 或 2，默认为 2): ").strip()
    choice = "2"

    if choice == "1":
        print("使用串行处理...")
        start_time = time.time()
        filter_videos(all_video_ids)
        elapsed = time.time() - start_time
        print(f"串行处理完成，耗时: {elapsed:.2f}秒，结果写入 filtered_videos.jsonl")
    else:
        print("使用并发处理...")
        # 可以自定义参数
        # max_workers = int(input("并发线程数 (默认20): ") or "20")
        # use_proxy = input("是否使用代理 (y/n，默认n): ").strip().lower() == "y"
        # timeout = int(input("请求超时时间/秒 (默认30): ") or "30")
        
        max_workers = 20
        use_proxy = True
        timeout = 30

        # 生成带时间戳的输出文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = f"filtered_videos_{timestamp}.jsonl"

        filter_videos_concurrent(
            all_video_ids,
            output_file=output_file,
            max_workers=max_workers,
            use_proxy=use_proxy,
            timeout=timeout
        )
        print(f"并发处理完成，结果写入 {output_file}")
